"""
页面结构调试工具 - 分析南京政策网站的实际HTML结构
"""

import sys
import os
import time
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crawler.nanjing_policy_crawler import NanjingPolicyCrawler
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


def save_page_source(driver, filename_prefix="page_source"):
    """保存页面源码到文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"debug/{filename_prefix}_{timestamp}.html"
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    try:
        page_source = driver.page_source
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(page_source)
        print(f"✅ 页面源码已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"❌ 保存页面源码失败: {e}")
        return None


def take_screenshot(driver, filename_prefix="screenshot"):
    """截取页面截图"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"debug/{filename_prefix}_{timestamp}.png"
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    try:
        driver.save_screenshot(filename)
        print(f"✅ 页面截图已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"❌ 保存截图失败: {e}")
        return None


def analyze_filter_elements(driver):
    """分析筛选区域的元素"""
    print("\n" + "="*60)
    print("           筛选元素分析")
    print("="*60)
    
    try:
        # 查找所有包含"市"的元素
        print("\n1. 查找所有包含'市'的元素:")
        city_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '市')]")
        for i, element in enumerate(city_elements[:10], 1):  # 只显示前10个
            try:
                tag_name = element.tag_name
                text = element.text.strip()
                classes = element.get_attribute('class') or 'None'
                id_attr = element.get_attribute('id') or 'None'
                print(f"  {i}. <{tag_name}> text='{text}' class='{classes}' id='{id_attr}'")
            except:
                print(f"  {i}. 元素已失效")
        
        # 查找可能的筛选容器
        print("\n2. 查找可能的筛选容器:")
        filter_containers = [
            "//div[contains(@class, 'filter')]",
            "//div[contains(@class, 'search')]", 
            "//div[contains(@class, 'condition')]",
            "//div[contains(@class, 'select')]",
            "//form",
            "//div[contains(@class, 'form')]"
        ]
        
        for selector in filter_containers:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    print(f"  找到 {len(elements)} 个元素: {selector}")
                    for i, elem in enumerate(elements[:3], 1):
                        classes = elem.get_attribute('class') or 'None'
                        print(f"    {i}. class='{classes}'")
            except:
                continue
        
        # 查找可能的展开按钮
        print("\n3. 查找可能的展开按钮:")
        expand_selectors = [
            "//*[contains(text(), '展开')]",
            "//*[contains(text(), '更多')]", 
            "//*[contains(@class, 'expand')]",
            "//*[contains(@class, 'more')]",
            "//*[contains(@class, 'toggle')]",
            "//i[contains(@class, 'arrow')]",
            "//span[contains(@class, 'arrow')]"
        ]
        
        for selector in expand_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    print(f"  找到 {len(elements)} 个元素: {selector}")
                    for i, elem in enumerate(elements[:3], 1):
                        tag_name = elem.tag_name
                        text = elem.text.strip()
                        classes = elem.get_attribute('class') or 'None'
                        print(f"    {i}. <{tag_name}> text='{text}' class='{classes}'")
            except:
                continue
        
        # 查找部门相关元素
        print("\n4. 查找部门相关元素:")
        departments = ["市发改委", "市科技局", "市工信局"]
        for dept in departments:
            try:
                elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{dept}')]")
                if elements:
                    print(f"  找到 {len(elements)} 个 '{dept}' 元素")
                    for i, elem in enumerate(elements[:2], 1):
                        tag_name = elem.tag_name
                        classes = elem.get_attribute('class') or 'None'
                        print(f"    {i}. <{tag_name}> class='{classes}'")
                else:
                    print(f"  未找到 '{dept}' 元素")
            except:
                print(f"  查找 '{dept}' 时出错")
                
    except Exception as e:
        print(f"❌ 分析筛选元素时出错: {e}")


def debug_page_structure():
    """调试页面结构的主函数"""
    print("="*70)
    print("           页面结构调试工具")
    print("="*70)
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("目标: 分析南京政策网站的实际HTML结构")
    print("="*70)
    
    crawler = None
    try:
        # 初始化爬虫
        print("\n1. 初始化爬虫...")
        crawler = NanjingPolicyCrawler()
        crawler.setup_driver()
        
        # 导航到目标页面
        print("2. 导航到目标页面...")
        if not crawler.navigate_to_target():
            print("❌ 导航失败")
            return False
        
        print("✅ 成功导航到目标页面")
        
        # 等待页面完全加载
        print("3. 等待页面完全加载...")
        time.sleep(5)
        
        # 保存初始页面源码和截图
        print("4. 保存初始页面状态...")
        save_page_source(crawler.driver, "initial_page")
        take_screenshot(crawler.driver, "initial_page")
        
        # 分析筛选元素
        analyze_filter_elements(crawler.driver)
        
        # 尝试点击第一行的"市"
        print("\n5. 尝试点击第一行的'市'...")
        try:
            # 使用之前成功的选择器
            city_element = crawler.driver.find_element(By.XPATH, "//div[1]//span[contains(text(), '市')]")
            print(f"✅ 找到'市'元素: <{city_element.tag_name}> class='{city_element.get_attribute('class')}'")
            
            city_element.click()
            print("✅ 成功点击'市'元素")
            
            # 等待第二行出现
            print("6. 等待第二行出现...")
            time.sleep(3)
            
            # 保存点击后的页面状态
            print("7. 保存点击后的页面状态...")
            save_page_source(crawler.driver, "after_city_click")
            take_screenshot(crawler.driver, "after_city_click")
            
            # 重新分析筛选元素
            print("8. 重新分析筛选元素...")
            analyze_filter_elements(crawler.driver)
            
        except Exception as e:
            print(f"❌ 点击'市'元素失败: {e}")
            save_page_source(crawler.driver, "click_failed")
            take_screenshot(crawler.driver, "click_failed")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        return False
    finally:
        if crawler:
            print("\n9. 保持浏览器打开30秒供手动检查...")
            time.sleep(30)
            crawler.cleanup()


if __name__ == "__main__":
    success = debug_page_structure()
    print("\n" + "="*70)
    if success:
        print("✅ 页面结构调试完成!")
        print("请查看debug目录中的HTML源码和截图文件")
    else:
        print("❌ 页面结构调试失败!")
    print("="*70)
