"""
Test crawler structure without requiring Selenium installation
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_crawler_initialization():
    """Test if crawler can be initialized (without Selenium)"""
    try:
        print("Testing crawler initialization...")
        
        # Import the crawler class
        from crawler.nanjing_policy_crawler import NanjingPolicyCrawler
        
        # Try to create an instance (this should work without Selenium)
        crawler = NanjingPolicyCrawler()
        print("✓ Crawler instance created successfully")
        
        # Test that logger is set up
        if hasattr(crawler, 'logger') and crawler.logger:
            print("✓ Logger initialized")
        else:
            print("✗ Logger not initialized")
            return False
        
        # Test that crawled_data is initialized
        if hasattr(crawler, 'crawled_data') and isinstance(crawler.crawled_data, list):
            print("✓ Crawled data list initialized")
        else:
            print("✗ Crawled data list not initialized")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error initializing crawler: {e}")
        return False

def test_utility_functions():
    """Test utility functions"""
    try:
        print("\nTesting utility functions...")
        
        from crawler.utils import (
            clean_text, 
            extract_text_from_html,
            generate_filename,
            validate_data
        )
        
        # Test clean_text
        test_text = "  This   is   a   test  \n\n  "
        cleaned = clean_text(test_text)
        expected = "This is a test"
        if cleaned == expected:
            print("✓ clean_text function works correctly")
        else:
            print(f"✗ clean_text failed: got '{cleaned}', expected '{expected}'")
            return False
        
        # Test extract_text_from_html
        html = "<div><p>Hello <strong>World</strong></p><script>alert('test')</script></div>"
        text = extract_text_from_html(html)
        if "Hello World" in text and "alert" not in text:
            print("✓ extract_text_from_html function works correctly")
        else:
            print(f"✗ extract_text_from_html failed: got '{text}'")
            return False
        
        # Test generate_filename
        filename = generate_filename("市发改委", "json", False)
        if "发改" in filename and filename.endswith(".json"):
            print("✓ generate_filename function works correctly")
        else:
            print(f"✗ generate_filename failed: got '{filename}'")
            return False
        
        # Test validate_data
        valid_data = {
            "title": "Test Policy",
            "create_time": "2024-01-01",
            "address": "http://test.com",
            "fbjg": "市发改委",
            "content": "Test content",
            "link": []
        }
        
        invalid_data = {
            "title": "",
            "create_time": "",
            "address": "",
            "fbjg": "",
            "content": "",
            "link": []
        }
        
        if validate_data(valid_data) and not validate_data(invalid_data):
            print("✓ validate_data function works correctly")
        else:
            print("✗ validate_data function failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing utility functions: {e}")
        return False

def test_data_saving():
    """Test data saving functionality"""
    try:
        print("\nTesting data saving functionality...")
        
        from crawler.utils import save_data_to_json, save_data_to_csv
        
        # Create test data
        test_data = [
            {
                "title": "测试政策1",
                "create_time": "2024-01-01",
                "address": "http://test1.com",
                "content": "测试内容1",
                "fbjg": "市发改委",
                "link": ["http://attachment1.pdf"]
            },
            {
                "title": "测试政策2", 
                "create_time": "2024-01-02",
                "address": "http://test2.com",
                "content": "测试内容2",
                "fbjg": "市科技局",
                "link": []
            }
        ]
        
        # Test JSON saving
        json_file = "data/test_output.json"
        save_data_to_json(test_data, json_file)
        
        if os.path.exists(json_file):
            # Verify JSON content
            with open(json_file, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
            
            if len(loaded_data) == 2 and loaded_data[0]["title"] == "测试政策1":
                print("✓ JSON saving works correctly")
                os.remove(json_file)  # Clean up
            else:
                print("✗ JSON saving failed - content mismatch")
                return False
        else:
            print("✗ JSON file was not created")
            return False
        
        # Test CSV saving
        csv_file = "data/test_output.csv"
        save_data_to_csv(test_data, csv_file)
        
        if os.path.exists(csv_file):
            print("✓ CSV saving works correctly")
            os.remove(csv_file)  # Clean up
        else:
            print("✗ CSV file was not created")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing data saving: {e}")
        return False

def test_configuration():
    """Test configuration values"""
    try:
        print("\nTesting configuration...")
        
        from crawler.config import (
            TARGET_URL,
            TARGET_DEPARTMENTS, 
            DATA_FIELDS,
            SELENIUM_CONFIG,
            OUTPUT_CONFIG
        )
        
        # Check target URL
        if TARGET_URL and "nanjing.gov.cn" in TARGET_URL:
            print("✓ Target URL is correctly configured")
        else:
            print("✗ Target URL configuration issue")
            return False
        
        # Check target departments
        expected_depts = ["市发改委", "市科技局", "市工信局"]
        if all(dept in TARGET_DEPARTMENTS for dept in expected_depts):
            print("✓ Target departments are correctly configured")
        else:
            print("✗ Target departments configuration issue")
            return False
        
        # Check data fields
        expected_fields = ["title", "create_time", "address", "content", "fbjg", "link"]
        if all(field in DATA_FIELDS for field in expected_fields):
            print("✓ Data fields are correctly configured")
        else:
            print("✗ Data fields configuration issue")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing configuration: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 70)
    print("           爬虫结构测试 (Crawler Structure Test)")
    print("=" * 70)
    
    tests = [
        test_configuration,
        test_utility_functions,
        test_data_saving,
        test_crawler_initialization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 70)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有结构测试通过! 爬虫代码结构正确。")
        print("下一步: 安装依赖包 (pip install -r requirements.txt)")
        print("然后运行: python main.py")
    else:
        print("✗ 部分测试失败，请检查代码结构。")
    
    print("=" * 70)
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
