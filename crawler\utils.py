"""
Utility functions for Nanjing Policy Crawler
"""

import os
import json
import csv
import logging
import random
import time
from datetime import datetime
from typing import List, Dict, Any
from bs4 import BeautifulSoup


def setup_logging(log_dir: str = "logs") -> logging.Logger:
    """Setup logging configuration"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"crawler_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)


def create_directories(dirs: List[str]) -> None:
    """Create necessary directories if they don't exist"""
    for directory in dirs:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")


def clean_text(text: str) -> str:
    """Clean and normalize text content"""
    if not text:
        return ""
    
    # Remove extra whitespace and normalize
    text = " ".join(text.split())
    return text.strip()


def extract_text_from_html(html_content: str) -> str:
    """Extract clean text from HTML content"""
    if not html_content:
        return ""
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.decompose()
    
    # Get text and clean it
    text = soup.get_text()
    return clean_text(text)


def save_data_to_json(data: List[Dict[str, Any]], filename: str) -> None:
    """Save data to JSON file"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"Data saved to JSON: {filename}")


def save_data_to_csv(data: List[Dict[str, Any]], filename: str) -> None:
    """Save data to CSV file"""
    if not data:
        return
    
    fieldnames = data[0].keys()
    with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    print(f"Data saved to CSV: {filename}")


def random_delay(min_seconds: int = 2, max_seconds: int = 5) -> None:
    """Add random delay to avoid being detected as bot"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)


def generate_filename(department: str, file_type: str = "json", include_timestamp: bool = True) -> str:
    """Generate filename for output data"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S") if include_timestamp else ""
    
    # Clean department name for filename
    clean_dept = department.replace("市", "").replace("委", "").replace("局", "")
    
    if include_timestamp:
        return f"nanjing_policies_{clean_dept}_{timestamp}.{file_type}"
    else:
        return f"nanjing_policies_{clean_dept}.{file_type}"


def validate_data(data: Dict[str, Any]) -> bool:
    """Validate extracted data"""
    required_fields = ["title", "create_time", "address", "fbjg"]
    
    for field in required_fields:
        if not data.get(field):
            return False
    
    return True
