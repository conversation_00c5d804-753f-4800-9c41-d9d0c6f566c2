"""
Nanjing Policy Crawler - Main crawler implementation
"""

import os
import time
import random
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    WebDriverException,
    ElementClickInterceptedException
)
from webdriver_manager.chrome import ChromeDriverManager

from .config import (
    TARGET_URL, 
    TARGET_DEPARTMENTS, 
    SELENIUM_CONFIG, 
    OUTPUT_CONFIG,
    USER_AGENTS
)
from .utils import (
    setup_logging,
    create_directories,
    clean_text,
    extract_text_from_html,
    save_data_to_json,
    save_data_to_csv,
    random_delay,
    generate_filename,
    validate_data
)


class NanjingPolicyCrawler:
    """Main crawler class for Nanjing government policy website"""
    
    def __init__(self):
        self.logger = setup_logging(OUTPUT_CONFIG["logs_dir"])
        self.driver = None
        self.wait = None
        self.crawled_data = []
        
        # Create necessary directories
        create_directories([OUTPUT_CONFIG["data_dir"], OUTPUT_CONFIG["logs_dir"]])
        
        self.logger.info("NanjingPolicyCrawler initialized")
    
    def setup_driver(self) -> None:
        """Setup Chrome WebDriver with appropriate options"""
        try:
            chrome_options = Options()
            
            # Add user agent
            user_agent = random.choice(USER_AGENTS)
            chrome_options.add_argument(f"--user-agent={user_agent}")
            
            # Additional options for stability
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            if SELENIUM_CONFIG["headless"]:
                chrome_options.add_argument("--headless")
            
            # Setup ChromeDriver with local driver path
            local_chromedriver_path = r"D:\chromedriver-win64\chromedriver-win64\chromedriver.exe"

            try:
                # First try to use local ChromeDriver
                if os.path.exists(local_chromedriver_path):
                    self.logger.info(f"Using local ChromeDriver: {local_chromedriver_path}")
                    service = Service(local_chromedriver_path)
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                else:
                    self.logger.info("Local ChromeDriver not found, trying ChromeDriverManager...")
                    # Fallback to ChromeDriverManager
                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e:
                self.logger.warning(f"Local ChromeDriver failed: {e}")
                self.logger.info("Trying system ChromeDriver...")

                # Final fallback to system ChromeDriver
                try:
                    self.driver = webdriver.Chrome(options=chrome_options)
                except Exception as e2:
                    self.logger.error(f"All ChromeDriver options failed: {e2}")
                    raise Exception(f"无法启动Chrome浏览器。请确保已安装Chrome浏览器和ChromeDriver。原始错误: {e}")
            
            # Configure timeouts
            self.driver.implicitly_wait(SELENIUM_CONFIG["implicit_wait"])
            self.driver.set_page_load_timeout(SELENIUM_CONFIG["page_load_timeout"])
            
            # Setup WebDriverWait
            self.wait = WebDriverWait(self.driver, SELENIUM_CONFIG["implicit_wait"])
            
            # Execute script to hide webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("WebDriver setup completed successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to setup WebDriver: {str(e)}")
            raise
    
    def navigate_to_target(self) -> bool:
        """Navigate to the target website"""
        try:
            self.logger.info(f"Navigating to: {TARGET_URL}")
            self.driver.get(TARGET_URL)
            
            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            self.logger.info("Successfully navigated to target website")
            return True
            
        except TimeoutException:
            self.logger.error("Timeout while loading the target website")
            return False
        except Exception as e:
            self.logger.error(f"Error navigating to target website: {str(e)}")
            return False
    
    def select_city_level(self) -> bool:
        """Select '市' (city) level - based on successful click.py implementation"""
        try:
            self.logger.info("Selecting city level using proven selector...")

            # Add delay before interaction
            random_delay(1, 2)

            # Use the proven CSS selector from click.py
            try:
                # Convert CSS selector to XPath equivalent
                city_element = self.wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "li[itemvalue='city']"))
                )
                city_element.click()
                self.logger.info("✅ Successfully selected city level using li[itemvalue='city']")

                # Wait for dynamic content to load (as per click.py)
                time.sleep(1)
                return True

            except Exception as e:
                self.logger.warning(f"CSS selector failed: {e}, trying XPath alternatives...")

                # Fallback XPath selectors
                xpath_selectors = [
                    "//li[@itemvalue='city']",
                    "//li[@itemvalue='city']//a",
                    "//a[@itemvalue='city']"
                ]

                for selector in xpath_selectors:
                    try:
                        self.logger.info(f"Trying XPath selector: {selector}")
                        city_element = self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        city_element.click()
                        self.logger.info(f"✅ Successfully selected city level using {selector}")
                        time.sleep(1)
                        return True
                    except Exception as e2:
                        self.logger.debug(f"XPath selector {selector} failed: {e2}")
                        continue

            self.logger.error("❌ All city level selectors failed")
            return False

        except Exception as e:
            self.logger.error(f"Error selecting city level: {str(e)}")
            return False

    def expand_department_filter(self) -> bool:
        """Expand the department filter - based on successful click.py implementation"""
        try:
            self.logger.info("Expanding department filter using proven selector...")

            # Add delay to ensure city selection has taken effect
            time.sleep(1)

            # Use the proven CSS selector from click.py with JavaScript click to avoid interception
            try:
                expand_element = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.slide"))
                )

                # Use JavaScript click to avoid element interception
                self.driver.execute_script("arguments[0].click();", expand_element)
                self.logger.info("✅ Successfully clicked expand button using JavaScript on div.slide")

                # Wait for department options to appear (as per click.py)
                time.sleep(2)
                return True

            except Exception as e:
                self.logger.warning(f"CSS selector div.slide failed: {e}, trying XPath alternatives...")

                # Fallback XPath selectors
                xpath_selectors = [
                    "//div[@class='slide']",
                    "//div[contains(@class, 'slide')]",
                    "//div[contains(text(), '展开')]",
                    "//span[contains(text(), '展开')]"
                ]

                for selector in xpath_selectors:
                    try:
                        self.logger.info(f"Trying XPath expand selector: {selector}")
                        expand_element = self.wait.until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                        # Use JavaScript click to avoid interception
                        self.driver.execute_script("arguments[0].click();", expand_element)
                        self.logger.info(f"✅ Successfully expanded using JavaScript on {selector}")
                        time.sleep(2)
                        return True
                    except Exception as e2:
                        self.logger.debug(f"XPath expand selector {selector} failed: {e2}")
                        continue

            # If no expand button found, it might already be expanded
            self.logger.warning("⚠️ No expand button found, department filter might already be expanded")
            return True

        except Exception as e:
            self.logger.error(f"Error expanding department filter: {str(e)}")
            return False

    def select_department(self, department: str) -> bool:
        """Select a specific department - based on successful click.py implementation"""
        try:
            self.logger.info(f"Selecting department: {department}")

            # Add delay before interaction
            time.sleep(0.5)

            # Use the proven CSS selector from click.py: ul#city-district li[title='{dept}']
            try:
                css_selector = f"ul#city-district li[title='{department}']"
                dept_element = self.wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, css_selector))
                )
                dept_element.click()
                self.logger.info(f"✅ Successfully selected department: {department} using CSS selector")

                # Wait for results to load (as per click.py)
                time.sleep(1.5)

                # Wait for policy list to appear
                try:
                    self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "li.market-list")))
                    self.logger.info("✅ Policy list loaded successfully")
                except:
                    self.logger.warning("⚠️ Policy list selector not found, but continuing...")

                return True

            except Exception as e:
                self.logger.warning(f"CSS selector failed: {e}, trying XPath alternatives...")

                # Fallback XPath selectors based on click.py pattern
                xpath_selectors = [
                    f"//ul[@id='city-district']//li[@title='{department}']",
                    f"//ul[@id='city-district']//li[contains(@title, '{department}')]",
                    f"//li[@title='{department}']",
                    f"//li[contains(@title, '{department}')]"
                ]

                for selector in xpath_selectors:
                    try:
                        self.logger.info(f"Trying XPath department selector: {selector}")
                        dept_element = self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        dept_element.click()
                        self.logger.info(f"✅ Successfully selected department: {department} using {selector}")
                        time.sleep(1.5)
                        return True
                    except Exception as e2:
                        self.logger.debug(f"XPath department selector {selector} failed: {e2}")
                        continue

            self.logger.error(f"❌ All department selectors failed for: {department}")
            return False

        except Exception as e:
            self.logger.error(f"Error selecting department {department}: {str(e)}")
            return False

    def apply_filters_for_department(self, department: str) -> bool:
        """Apply complete filtering process for a specific department"""
        try:
            self.logger.info(f"Applying complete filter process for department: {department}")

            # Step 1: Select city level in first row
            self.logger.info("Step 1: Selecting city level in first row...")
            if not self.select_city_level():
                self.logger.error("Failed to select city level")
                return False

            # Step 2: Wait for second row to appear and expand department filter
            self.logger.info("Step 2: Expanding department filter in second row...")
            if not self.expand_department_filter():
                self.logger.warning("Failed to expand department filter, continuing anyway...")

            # Step 3: Select specific department
            self.logger.info(f"Step 3: Selecting department {department}...")
            if not self.select_department(department):
                self.logger.error(f"Failed to select department: {department}")
                return False

            # Step 4: Wait for results to load
            self.logger.info("Step 4: Waiting for filtered results to load...")
            random_delay(3, 6)

            return True

        except Exception as e:
            self.logger.error(f"Error applying filters for department {department}: {str(e)}")
            return False

    def get_policy_links(self) -> List[str]:
        """Extract policy links from the current page - based on click.py pattern"""
        try:
            self.logger.info("Extracting policy links using proven selectors...")

            # Wait for policy list to load (as per click.py)
            time.sleep(1.5)

            # Use the proven selector from click.py: li.market-list
            policy_links = []

            try:
                # Wait for policy list elements to be present
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "li.market-list")))

                # Get all policy list items
                policy_items = self.driver.find_elements(By.CSS_SELECTOR, "li.market-list")
                self.logger.info(f"Found {len(policy_items)} policy items")

                for item in policy_items:
                    try:
                        # Look for the title element with onclick attribute (as per click.py)
                        title_element = item.find_element(By.CSS_SELECTOR, "p.market-text")
                        onclick = title_element.get_attribute("onclick")

                        if onclick and "window.open" in onclick:
                            # Extract URL from onclick attribute: window.open('url')
                            url_start = onclick.find("'") + 1
                            url_end = onclick.find("'", url_start)
                            if url_start > 0 and url_end > url_start:
                                relative_url = onclick[url_start:url_end]
                                # Convert to absolute URL
                                base_url = "https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/"
                                full_url = base_url + relative_url if not relative_url.startswith('http') else relative_url
                                policy_links.append(full_url)

                    except Exception as e:
                        self.logger.debug(f"Error extracting link from policy item: {e}")
                        continue

                self.logger.info(f"✅ Successfully extracted {len(policy_links)} policy links")
                return policy_links

            except Exception as e:
                self.logger.warning(f"CSS selector failed: {e}, trying XPath fallback...")

                # Fallback XPath selectors
                xpath_selectors = [
                    "//li[contains(@class, 'market-list')]//p[contains(@class, 'market-text')]",
                    "//li[contains(@class, 'market-list')]//a",
                    "//div[contains(@class, 'market')]//a"
                ]

                for selector in xpath_selectors:
                    try:
                        link_elements = self.driver.find_elements(By.XPATH, selector)
                        if link_elements:
                            for element in link_elements:
                                href = element.get_attribute('href')
                                onclick = element.get_attribute('onclick')

                                if href and href not in policy_links:
                                    policy_links.append(href)
                                elif onclick and "window.open" in onclick:
                                    # Extract from onclick
                                    url_start = onclick.find("'") + 1
                                    url_end = onclick.find("'", url_start)
                                    if url_start > 0 and url_end > url_start:
                                        relative_url = onclick[url_start:url_end]
                                        base_url = "https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/"
                                        full_url = base_url + relative_url if not relative_url.startswith('http') else relative_url
                                        if full_url not in policy_links:
                                            policy_links.append(full_url)
                            break
                    except:
                        continue

                self.logger.info(f"Found {len(policy_links)} policy links using fallback")
                return policy_links

        except Exception as e:
            self.logger.error(f"Error extracting policy links: {str(e)}")
            return []

    def extract_policy_data(self, policy_url: str) -> Optional[Dict[str, Any]]:
        """Extract data from a single policy detail page"""
        try:
            self.logger.info(f"Extracting data from: {policy_url}")

            # Navigate to policy detail page
            self.driver.get(policy_url)
            random_delay(*SELENIUM_CONFIG["request_delay"])

            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            # Initialize data dictionary
            policy_data = {
                "title": "",
                "create_time": "",
                "address": policy_url,
                "content": "",
                "fbjg": "",
                "link": []
            }

            # Extract title
            title_selectors = [
                "//h1",
                "//h2",
                "//div[contains(@class, 'title')]",
                "//div[contains(@class, 'head')]//h1",
                "//div[contains(@class, 'head')]//h2",
                "//span[contains(@class, 'title')]"
            ]

            for selector in title_selectors:
                try:
                    title_element = self.driver.find_element(By.XPATH, selector)
                    policy_data["title"] = clean_text(title_element.text)
                    if policy_data["title"]:
                        break
                except:
                    continue

            # Extract create_time (publication date)
            time_selectors = [
                "//span[contains(text(), '发布时间')]/..//span[2]",
                "//div[contains(text(), '发布时间')]/..//span",
                "//span[contains(text(), '时间')]",
                "//div[contains(@class, 'time')]",
                "//div[contains(@class, 'date')]",
                "//span[contains(@class, 'date')]"
            ]

            for selector in time_selectors:
                try:
                    time_element = self.driver.find_element(By.XPATH, selector)
                    time_text = clean_text(time_element.text)
                    if time_text and any(char.isdigit() for char in time_text):
                        policy_data["create_time"] = time_text
                        break
                except:
                    continue

            # Extract fbjg (publishing organization)
            fbjg_selectors = [
                "//span[contains(text(), '发布机构')]/..//span[2]",
                "//div[contains(text(), '发布机构')]/..//span",
                "//span[contains(text(), '机构')]",
                "//div[contains(@class, 'org')]",
                "//div[contains(@class, 'department')]"
            ]

            for selector in fbjg_selectors:
                try:
                    fbjg_element = self.driver.find_element(By.XPATH, selector)
                    policy_data["fbjg"] = clean_text(fbjg_element.text)
                    if policy_data["fbjg"]:
                        break
                except:
                    continue

            # Extract content
            content_selectors = [
                "//div[contains(@class, 'content')]",
                "//div[contains(@class, 'detail')]",
                "//div[contains(@class, 'text')]",
                "//div[contains(@class, 'body')]",
                "//article",
                "//main"
            ]

            for selector in content_selectors:
                try:
                    content_element = self.driver.find_element(By.XPATH, selector)
                    content_html = content_element.get_attribute('innerHTML')
                    policy_data["content"] = extract_text_from_html(content_html)
                    if policy_data["content"]:
                        break
                except:
                    continue

            # Extract attachment links
            link_selectors = [
                "//a[contains(@href, '.pdf')]",
                "//a[contains(@href, '.doc')]",
                "//a[contains(@href, '.docx')]",
                "//a[contains(text(), '附件')]",
                "//a[contains(text(), '下载')]",
                "//div[contains(@class, 'attachment')]//a"
            ]

            attachment_links = []
            for selector in link_selectors:
                try:
                    link_elements = self.driver.find_elements(By.XPATH, selector)
                    for element in link_elements:
                        href = element.get_attribute('href')
                        if href and href not in attachment_links:
                            attachment_links.append(href)
                except:
                    continue

            policy_data["link"] = attachment_links

            # Validate extracted data
            if validate_data(policy_data):
                self.logger.info(f"Successfully extracted data for: {policy_data['title'][:50]}...")
                return policy_data
            else:
                self.logger.warning(f"Incomplete data extracted from: {policy_url}")
                return policy_data  # Return even if incomplete

        except Exception as e:
            self.logger.error(f"Error extracting policy data from {policy_url}: {str(e)}")
            return None

    def crawl_department_policies(self, department: str) -> List[Dict[str, Any]]:
        """Crawl policies for a specific department"""
        try:
            self.logger.info(f"Starting to crawl policies for department: {department}")

            department_data = []

            # Navigate to target page
            if not self.navigate_to_target():
                return department_data

            # Apply complete filtering process for this department
            if not self.apply_filters_for_department(department):
                self.logger.error(f"Failed to apply filters for department: {department}")
                return department_data

            # Get policy links
            policy_links = self.get_policy_links()

            if not policy_links:
                self.logger.warning(f"No policy links found for department: {department}")
                return department_data

            # Extract data from each policy
            for i, link in enumerate(policy_links, 1):
                self.logger.info(f"Processing policy {i}/{len(policy_links)} for {department}")

                policy_data = self.extract_policy_data(link)
                if policy_data:
                    # Ensure fbjg is set to the department if not found
                    if not policy_data["fbjg"]:
                        policy_data["fbjg"] = department

                    department_data.append(policy_data)

                # Add delay between requests
                random_delay(*SELENIUM_CONFIG["request_delay"])

            self.logger.info(f"Completed crawling for {department}. Found {len(department_data)} policies.")
            return department_data

        except Exception as e:
            self.logger.error(f"Error crawling department {department}: {str(e)}")
            return []

    def save_department_data(self, department: str, data: List[Dict[str, Any]]) -> None:
        """Save data for a specific department"""
        try:
            if not data:
                self.logger.warning(f"No data to save for department: {department}")
                return

            data_dir = OUTPUT_CONFIG["data_dir"]

            # Save as JSON
            if OUTPUT_CONFIG["json_output"]:
                json_filename = os.path.join(
                    data_dir,
                    generate_filename(department, "json", OUTPUT_CONFIG["include_timestamp"])
                )
                save_data_to_json(data, json_filename)

            # Save as CSV
            if OUTPUT_CONFIG["csv_output"]:
                csv_filename = os.path.join(
                    data_dir,
                    generate_filename(department, "csv", OUTPUT_CONFIG["include_timestamp"])
                )
                save_data_to_csv(data, csv_filename)

            self.logger.info(f"Data saved for department: {department}")

        except Exception as e:
            self.logger.error(f"Error saving data for department {department}: {str(e)}")

    def run_crawler(self) -> Dict[str, List[Dict[str, Any]]]:
        """Main method to run the crawler for all target departments"""
        try:
            self.logger.info("Starting Nanjing Policy Crawler...")

            # Setup WebDriver
            self.setup_driver()

            all_data = {}

            # Crawl each department
            for department in TARGET_DEPARTMENTS:
                self.logger.info(f"Processing department: {department}")

                department_data = self.crawl_department_policies(department)
                all_data[department] = department_data

                # Save data for this department
                self.save_department_data(department, department_data)

                # Add delay between departments
                random_delay(5, 10)

            # Save combined data
            combined_data = []
            for dept_data in all_data.values():
                combined_data.extend(dept_data)

            if combined_data:
                combined_filename = os.path.join(
                    OUTPUT_CONFIG["data_dir"],
                    f"nanjing_policies_all_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                )
                save_data_to_json(combined_data, combined_filename)

            self.logger.info("Crawler completed successfully!")
            return all_data

        except Exception as e:
            self.logger.error(f"Error running crawler: {str(e)}")
            return {}
        finally:
            self.cleanup()

    def cleanup(self) -> None:
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
                self.logger.info("WebDriver closed successfully")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")
