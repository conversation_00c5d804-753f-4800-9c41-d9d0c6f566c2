"""
Nanjing Policy Crawler - Main crawler implementation
"""

import os
import time
import random
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    WebDriverException,
    ElementClickInterceptedException
)
from webdriver_manager.chrome import ChromeDriverManager

from .config import (
    TARGET_URL, 
    TARGET_DEPARTMENTS, 
    SELENIUM_CONFIG, 
    OUTPUT_CONFIG,
    USER_AGENTS
)
from .utils import (
    setup_logging,
    create_directories,
    clean_text,
    extract_text_from_html,
    save_data_to_json,
    save_data_to_csv,
    random_delay,
    generate_filename,
    validate_data
)


class NanjingPolicyCrawler:
    """Main crawler class for Nanjing government policy website"""
    
    def __init__(self):
        self.logger = setup_logging(OUTPUT_CONFIG["logs_dir"])
        self.driver = None
        self.wait = None
        self.crawled_data = []
        
        # Create necessary directories
        create_directories([OUTPUT_CONFIG["data_dir"], OUTPUT_CONFIG["logs_dir"]])
        
        self.logger.info("NanjingPolicyCrawler initialized")
    
    def setup_driver(self) -> None:
        """Setup Chrome WebDriver with appropriate options"""
        try:
            chrome_options = Options()
            
            # Add user agent
            user_agent = random.choice(USER_AGENTS)
            chrome_options.add_argument(f"--user-agent={user_agent}")
            
            # Additional options for stability
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            if SELENIUM_CONFIG["headless"]:
                chrome_options.add_argument("--headless")
            
            # Setup ChromeDriver with fallback options
            try:
                # Try to use ChromeDriverManager first
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e:
                self.logger.warning(f"ChromeDriverManager failed: {e}")
                self.logger.info("Trying to use system ChromeDriver...")

                # Fallback to system ChromeDriver
                try:
                    self.driver = webdriver.Chrome(options=chrome_options)
                except Exception as e2:
                    self.logger.error(f"System ChromeDriver also failed: {e2}")
                    raise Exception(f"无法启动Chrome浏览器。请确保已安装Chrome浏览器和ChromeDriver。原始错误: {e}")
            
            # Configure timeouts
            self.driver.implicitly_wait(SELENIUM_CONFIG["implicit_wait"])
            self.driver.set_page_load_timeout(SELENIUM_CONFIG["page_load_timeout"])
            
            # Setup WebDriverWait
            self.wait = WebDriverWait(self.driver, SELENIUM_CONFIG["implicit_wait"])
            
            # Execute script to hide webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("WebDriver setup completed successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to setup WebDriver: {str(e)}")
            raise
    
    def navigate_to_target(self) -> bool:
        """Navigate to the target website"""
        try:
            self.logger.info(f"Navigating to: {TARGET_URL}")
            self.driver.get(TARGET_URL)
            
            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            self.logger.info("Successfully navigated to target website")
            return True
            
        except TimeoutException:
            self.logger.error("Timeout while loading the target website")
            return False
        except Exception as e:
            self.logger.error(f"Error navigating to target website: {str(e)}")
            return False
    
    def select_city_level(self) -> bool:
        """Select '市' (city) level in the first row of filters"""
        try:
            self.logger.info("Selecting city level filter...")
            
            # Add delay before interaction
            random_delay(*SELENIUM_CONFIG["request_delay"])
            
            # Look for city level selector - this will need to be adjusted based on actual page structure
            # Common selectors to try
            selectors_to_try = [
                "//span[contains(text(), '市')]",
                "//div[contains(@class, 'level')]//span[contains(text(), '市')]",
                "//button[contains(text(), '市')]",
                "//a[contains(text(), '市')]"
            ]
            
            for selector in selectors_to_try:
                try:
                    city_element = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    city_element.click()
                    self.logger.info("Successfully selected city level")
                    random_delay(1, 3)
                    return True
                except:
                    continue
            
            self.logger.warning("Could not find city level selector")
            return False
            
        except Exception as e:
            self.logger.error(f"Error selecting city level: {str(e)}")
            return False

    def expand_department_filter(self) -> bool:
        """Expand the department filter section"""
        try:
            self.logger.info("Expanding department filter...")

            # Look for expand button or clickable element
            expand_selectors = [
                "//div[contains(@class, 'expand')]",
                "//span[contains(@class, 'expand')]",
                "//button[contains(@class, 'expand')]",
                "//i[contains(@class, 'expand')]",
                "//div[contains(text(), '展开')]",
                "//span[contains(text(), '展开')]"
            ]

            for selector in expand_selectors:
                try:
                    expand_element = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    expand_element.click()
                    self.logger.info("Successfully expanded department filter")
                    random_delay(1, 3)
                    return True
                except:
                    continue

            self.logger.warning("Could not find expand button, filter might already be expanded")
            return True  # Assume it's already expanded

        except Exception as e:
            self.logger.error(f"Error expanding department filter: {str(e)}")
            return False

    def select_department(self, department: str) -> bool:
        """Select a specific department"""
        try:
            self.logger.info(f"Selecting department: {department}")

            # Add delay before interaction
            random_delay(*SELENIUM_CONFIG["request_delay"])

            # Look for department selector
            department_selectors = [
                f"//span[contains(text(), '{department}')]",
                f"//div[contains(text(), '{department}')]",
                f"//button[contains(text(), '{department}')]",
                f"//a[contains(text(), '{department}')]",
                f"//label[contains(text(), '{department}')]",
                f"//input[@value='{department}']/..//span",
                f"//li[contains(text(), '{department}')]"
            ]

            for selector in department_selectors:
                try:
                    dept_element = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    dept_element.click()
                    self.logger.info(f"Successfully selected department: {department}")
                    random_delay(2, 4)
                    return True
                except:
                    continue

            self.logger.error(f"Could not find department selector for: {department}")
            return False

        except Exception as e:
            self.logger.error(f"Error selecting department {department}: {str(e)}")
            return False

    def get_policy_links(self) -> List[str]:
        """Extract policy links from the current page"""
        try:
            self.logger.info("Extracting policy links...")

            # Wait for policy list to load
            random_delay(3, 5)

            # Common selectors for policy links
            link_selectors = [
                "//a[contains(@href, 'policy')]",
                "//a[contains(@href, 'detail')]",
                "//div[contains(@class, 'policy')]//a",
                "//div[contains(@class, 'list')]//a",
                "//tr//a",
                "//li//a"
            ]

            policy_links = []

            for selector in link_selectors:
                try:
                    link_elements = self.driver.find_elements(By.XPATH, selector)
                    if link_elements:
                        for element in link_elements:
                            href = element.get_attribute('href')
                            if href and href not in policy_links:
                                policy_links.append(href)
                        break
                except:
                    continue

            self.logger.info(f"Found {len(policy_links)} policy links")
            return policy_links

        except Exception as e:
            self.logger.error(f"Error extracting policy links: {str(e)}")
            return []

    def extract_policy_data(self, policy_url: str) -> Optional[Dict[str, Any]]:
        """Extract data from a single policy detail page"""
        try:
            self.logger.info(f"Extracting data from: {policy_url}")

            # Navigate to policy detail page
            self.driver.get(policy_url)
            random_delay(*SELENIUM_CONFIG["request_delay"])

            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            # Initialize data dictionary
            policy_data = {
                "title": "",
                "create_time": "",
                "address": policy_url,
                "content": "",
                "fbjg": "",
                "link": []
            }

            # Extract title
            title_selectors = [
                "//h1",
                "//h2",
                "//div[contains(@class, 'title')]",
                "//div[contains(@class, 'head')]//h1",
                "//div[contains(@class, 'head')]//h2",
                "//span[contains(@class, 'title')]"
            ]

            for selector in title_selectors:
                try:
                    title_element = self.driver.find_element(By.XPATH, selector)
                    policy_data["title"] = clean_text(title_element.text)
                    if policy_data["title"]:
                        break
                except:
                    continue

            # Extract create_time (publication date)
            time_selectors = [
                "//span[contains(text(), '发布时间')]/..//span[2]",
                "//div[contains(text(), '发布时间')]/..//span",
                "//span[contains(text(), '时间')]",
                "//div[contains(@class, 'time')]",
                "//div[contains(@class, 'date')]",
                "//span[contains(@class, 'date')]"
            ]

            for selector in time_selectors:
                try:
                    time_element = self.driver.find_element(By.XPATH, selector)
                    time_text = clean_text(time_element.text)
                    if time_text and any(char.isdigit() for char in time_text):
                        policy_data["create_time"] = time_text
                        break
                except:
                    continue

            # Extract fbjg (publishing organization)
            fbjg_selectors = [
                "//span[contains(text(), '发布机构')]/..//span[2]",
                "//div[contains(text(), '发布机构')]/..//span",
                "//span[contains(text(), '机构')]",
                "//div[contains(@class, 'org')]",
                "//div[contains(@class, 'department')]"
            ]

            for selector in fbjg_selectors:
                try:
                    fbjg_element = self.driver.find_element(By.XPATH, selector)
                    policy_data["fbjg"] = clean_text(fbjg_element.text)
                    if policy_data["fbjg"]:
                        break
                except:
                    continue

            # Extract content
            content_selectors = [
                "//div[contains(@class, 'content')]",
                "//div[contains(@class, 'detail')]",
                "//div[contains(@class, 'text')]",
                "//div[contains(@class, 'body')]",
                "//article",
                "//main"
            ]

            for selector in content_selectors:
                try:
                    content_element = self.driver.find_element(By.XPATH, selector)
                    content_html = content_element.get_attribute('innerHTML')
                    policy_data["content"] = extract_text_from_html(content_html)
                    if policy_data["content"]:
                        break
                except:
                    continue

            # Extract attachment links
            link_selectors = [
                "//a[contains(@href, '.pdf')]",
                "//a[contains(@href, '.doc')]",
                "//a[contains(@href, '.docx')]",
                "//a[contains(text(), '附件')]",
                "//a[contains(text(), '下载')]",
                "//div[contains(@class, 'attachment')]//a"
            ]

            attachment_links = []
            for selector in link_selectors:
                try:
                    link_elements = self.driver.find_elements(By.XPATH, selector)
                    for element in link_elements:
                        href = element.get_attribute('href')
                        if href and href not in attachment_links:
                            attachment_links.append(href)
                except:
                    continue

            policy_data["link"] = attachment_links

            # Validate extracted data
            if validate_data(policy_data):
                self.logger.info(f"Successfully extracted data for: {policy_data['title'][:50]}...")
                return policy_data
            else:
                self.logger.warning(f"Incomplete data extracted from: {policy_url}")
                return policy_data  # Return even if incomplete

        except Exception as e:
            self.logger.error(f"Error extracting policy data from {policy_url}: {str(e)}")
            return None

    def crawl_department_policies(self, department: str) -> List[Dict[str, Any]]:
        """Crawl policies for a specific department"""
        try:
            self.logger.info(f"Starting to crawl policies for department: {department}")

            department_data = []

            # Navigate to target page
            if not self.navigate_to_target():
                return department_data

            # Select city level
            if not self.select_city_level():
                self.logger.warning("Failed to select city level, continuing anyway...")

            # Expand department filter
            if not self.expand_department_filter():
                self.logger.warning("Failed to expand department filter, continuing anyway...")

            # Select specific department
            if not self.select_department(department):
                self.logger.error(f"Failed to select department: {department}")
                return department_data

            # Get policy links
            policy_links = self.get_policy_links()

            if not policy_links:
                self.logger.warning(f"No policy links found for department: {department}")
                return department_data

            # Extract data from each policy
            for i, link in enumerate(policy_links, 1):
                self.logger.info(f"Processing policy {i}/{len(policy_links)} for {department}")

                policy_data = self.extract_policy_data(link)
                if policy_data:
                    # Ensure fbjg is set to the department if not found
                    if not policy_data["fbjg"]:
                        policy_data["fbjg"] = department

                    department_data.append(policy_data)

                # Add delay between requests
                random_delay(*SELENIUM_CONFIG["request_delay"])

            self.logger.info(f"Completed crawling for {department}. Found {len(department_data)} policies.")
            return department_data

        except Exception as e:
            self.logger.error(f"Error crawling department {department}: {str(e)}")
            return []

    def save_department_data(self, department: str, data: List[Dict[str, Any]]) -> None:
        """Save data for a specific department"""
        try:
            if not data:
                self.logger.warning(f"No data to save for department: {department}")
                return

            data_dir = OUTPUT_CONFIG["data_dir"]

            # Save as JSON
            if OUTPUT_CONFIG["json_output"]:
                json_filename = os.path.join(
                    data_dir,
                    generate_filename(department, "json", OUTPUT_CONFIG["include_timestamp"])
                )
                save_data_to_json(data, json_filename)

            # Save as CSV
            if OUTPUT_CONFIG["csv_output"]:
                csv_filename = os.path.join(
                    data_dir,
                    generate_filename(department, "csv", OUTPUT_CONFIG["include_timestamp"])
                )
                save_data_to_csv(data, csv_filename)

            self.logger.info(f"Data saved for department: {department}")

        except Exception as e:
            self.logger.error(f"Error saving data for department {department}: {str(e)}")

    def run_crawler(self) -> Dict[str, List[Dict[str, Any]]]:
        """Main method to run the crawler for all target departments"""
        try:
            self.logger.info("Starting Nanjing Policy Crawler...")

            # Setup WebDriver
            self.setup_driver()

            all_data = {}

            # Crawl each department
            for department in TARGET_DEPARTMENTS:
                self.logger.info(f"Processing department: {department}")

                department_data = self.crawl_department_policies(department)
                all_data[department] = department_data

                # Save data for this department
                self.save_department_data(department, department_data)

                # Add delay between departments
                random_delay(5, 10)

            # Save combined data
            combined_data = []
            for dept_data in all_data.values():
                combined_data.extend(dept_data)

            if combined_data:
                combined_filename = os.path.join(
                    OUTPUT_CONFIG["data_dir"],
                    f"nanjing_policies_all_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                )
                save_data_to_json(combined_data, combined_filename)

            self.logger.info("Crawler completed successfully!")
            return all_data

        except Exception as e:
            self.logger.error(f"Error running crawler: {str(e)}")
            return {}
        finally:
            self.cleanup()

    def cleanup(self) -> None:
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
                self.logger.info("WebDriver closed successfully")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")
