<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="64">
            <item index="0" class="java.lang.String" itemvalue="httpx" />
            <item index="1" class="java.lang.String" itemvalue="tiktoken" />
            <item index="2" class="java.lang.String" itemvalue="protobuf" />
            <item index="3" class="java.lang.String" itemvalue="greenlet" />
            <item index="4" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="5" class="java.lang.String" itemvalue="h11" />
            <item index="6" class="java.lang.String" itemvalue="marshmallow" />
            <item index="7" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="8" class="java.lang.String" itemvalue="gitdb" />
            <item index="9" class="java.lang.String" itemvalue="markdown-it-py" />
            <item index="10" class="java.lang.String" itemvalue="frozenlist" />
            <item index="11" class="java.lang.String" itemvalue="langchain-community" />
            <item index="12" class="java.lang.String" itemvalue="langchain" />
            <item index="13" class="java.lang.String" itemvalue="certifi" />
            <item index="14" class="java.lang.String" itemvalue="anyio" />
            <item index="15" class="java.lang.String" itemvalue="jsonschema" />
            <item index="16" class="java.lang.String" itemvalue="GitPython" />
            <item index="17" class="java.lang.String" itemvalue="pydantic" />
            <item index="18" class="java.lang.String" itemvalue="streamlit" />
            <item index="19" class="java.lang.String" itemvalue="orjson" />
            <item index="20" class="java.lang.String" itemvalue="click" />
            <item index="21" class="java.lang.String" itemvalue="altair" />
            <item index="22" class="java.lang.String" itemvalue="attrs" />
            <item index="23" class="java.lang.String" itemvalue="dataclasses-json" />
            <item index="24" class="java.lang.String" itemvalue="openai" />
            <item index="25" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="26" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="27" class="java.lang.String" itemvalue="langsmith" />
            <item index="28" class="java.lang.String" itemvalue="jsonpatch" />
            <item index="29" class="java.lang.String" itemvalue="httpcore" />
            <item index="30" class="java.lang.String" itemvalue="idna" />
            <item index="31" class="java.lang.String" itemvalue="referencing" />
            <item index="32" class="java.lang.String" itemvalue="distro" />
            <item index="33" class="java.lang.String" itemvalue="langchain-openai" />
            <item index="34" class="java.lang.String" itemvalue="async-timeout" />
            <item index="35" class="java.lang.String" itemvalue="pydeck" />
            <item index="36" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="37" class="java.lang.String" itemvalue="mypy-extensions" />
            <item index="38" class="java.lang.String" itemvalue="numpy" />
            <item index="39" class="java.lang.String" itemvalue="requests" />
            <item index="40" class="java.lang.String" itemvalue="Jinja2" />
            <item index="41" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="42" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="43" class="java.lang.String" itemvalue="mdurl" />
            <item index="44" class="java.lang.String" itemvalue="blinker" />
            <item index="45" class="java.lang.String" itemvalue="pyarrow" />
            <item index="46" class="java.lang.String" itemvalue="annotated-types" />
            <item index="47" class="java.lang.String" itemvalue="faiss-cpu" />
            <item index="48" class="java.lang.String" itemvalue="jsonpointer" />
            <item index="49" class="java.lang.String" itemvalue="rich" />
            <item index="50" class="java.lang.String" itemvalue="packaging" />
            <item index="51" class="java.lang.String" itemvalue="pandas" />
            <item index="52" class="java.lang.String" itemvalue="tqdm" />
            <item index="53" class="java.lang.String" itemvalue="typing-inspect" />
            <item index="54" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="55" class="java.lang.String" itemvalue="cachetools" />
            <item index="56" class="java.lang.String" itemvalue="langchain-core" />
            <item index="57" class="java.lang.String" itemvalue="pillow" />
            <item index="58" class="java.lang.String" itemvalue="aiohttp" />
            <item index="59" class="java.lang.String" itemvalue="multidict" />
            <item index="60" class="java.lang.String" itemvalue="langchain-text-splitters" />
            <item index="61" class="java.lang.String" itemvalue="yarl" />
            <item index="62" class="java.lang.String" itemvalue="aiosignal" />
            <item index="63" class="java.lang.String" itemvalue="transformers" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="list.add" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>