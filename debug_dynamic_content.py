"""
动态内容调试工具 - 分析选择"市"级别后的动态变化
"""

import sys
import os
import time
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crawler.nanjing_policy_crawler import NanjingPolicyCrawler
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains


def save_page_source(driver, filename_prefix="page_source"):
    """保存页面源码到文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"debug/{filename_prefix}_{timestamp}.html"
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    try:
        page_source = driver.page_source
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(page_source)
        print(f"✅ 页面源码已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"❌ 保存页面源码失败: {e}")
        return None


def take_screenshot(driver, filename_prefix="screenshot"):
    """截取页面截图"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"debug/{filename_prefix}_{timestamp}.png"
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    try:
        driver.save_screenshot(filename)
        print(f"✅ 页面截图已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"❌ 保存截图失败: {e}")
        return None


def wait_for_dynamic_content(driver, timeout=10):
    """等待动态内容加载"""
    print(f"等待动态内容加载 ({timeout}秒)...")
    
    # 等待可能的AJAX请求完成
    for i in range(timeout):
        time.sleep(1)
        print(f"  等待中... {i+1}/{timeout}")
        
        # 检查是否有新的元素出现
        try:
            # 查找可能的发布机构相关元素
            dept_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '发布机构') or contains(text(), '部门') or contains(text(), '发改') or contains(text(), '科技') or contains(text(), '工信')]")
            if dept_elements:
                print(f"  发现可能的部门相关元素: {len(dept_elements)} 个")
                return True
        except:
            pass
    
    print("  动态内容等待完成")
    return False


def analyze_city_selection_effect(driver):
    """分析选择"市"级别后的效果"""
    print("\n" + "="*60)
    print("           分析选择'市'级别后的动态变化")
    print("="*60)
    
    try:
        # 1. 保存选择前的状态
        print("\n1. 保存选择前的页面状态...")
        save_page_source(driver, "before_city_selection")
        take_screenshot(driver, "before_city_selection")
        
        # 2. 查找并点击"市"级别选项
        print("\n2. 查找并点击'市'级别选项...")
        
        # 尝试多种选择器来找到"市"选项
        city_selectors = [
            "//li[@itemvalue='city']//a",
            "//li[@itemvalue='city']",
            "//a[@itemvalue='city']",
            "//li[contains(text(), '市')]",
            "//a[contains(text(), '市')]",
            "//span[contains(text(), '市')]"
        ]
        
        city_clicked = False
        for selector in city_selectors:
            try:
                print(f"  尝试选择器: {selector}")
                city_element = driver.find_element(By.XPATH, selector)
                
                # 滚动到元素可见
                driver.execute_script("arguments[0].scrollIntoView(true);", city_element)
                time.sleep(1)
                
                # 尝试点击
                city_element.click()
                print(f"  ✅ 成功点击'市'选项")
                city_clicked = True
                break
                
            except Exception as e:
                print(f"  ❌ 选择器失败: {e}")
                continue
        
        if not city_clicked:
            print("  ❌ 无法找到或点击'市'选项")
            return False
        
        # 3. 等待页面响应
        print("\n3. 等待页面响应...")
        time.sleep(3)
        
        # 4. 等待动态内容加载
        print("\n4. 等待动态内容加载...")
        wait_for_dynamic_content(driver, 10)
        
        # 5. 保存选择后的状态
        print("\n5. 保存选择后的页面状态...")
        save_page_source(driver, "after_city_selection")
        take_screenshot(driver, "after_city_selection")
        
        # 6. 分析页面变化
        print("\n6. 分析页面变化...")
        analyze_page_changes(driver)
        
        # 7. 尝试查找发布机构选项
        print("\n7. 查找发布机构选项...")
        find_department_options(driver)
        
        return True
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        return False


def analyze_page_changes(driver):
    """分析页面变化"""
    try:
        # 查找所有可能的新增元素
        print("  分析新增的页面元素...")
        
        # 查找所有包含"发布"、"机构"、"部门"的元素
        keywords = ["发布", "机构", "部门", "发改", "科技", "工信"]
        
        for keyword in keywords:
            try:
                elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                if elements:
                    print(f"    找到包含'{keyword}'的元素: {len(elements)} 个")
                    for i, elem in enumerate(elements[:3], 1):
                        try:
                            tag = elem.tag_name
                            text = elem.text.strip()[:50]
                            classes = elem.get_attribute('class') or 'None'
                            print(f"      {i}. <{tag}> text='{text}' class='{classes}'")
                        except:
                            print(f"      {i}. 元素已失效")
            except:
                continue
        
        # 查找所有隐藏的元素
        print("  查找隐藏的元素...")
        hidden_elements = driver.find_elements(By.XPATH, "//*[contains(@style, 'display: none') or contains(@class, 'hidden')]")
        print(f"    找到隐藏元素: {len(hidden_elements)} 个")
        
        # 查找所有新的下拉列表或选择器
        print("  查找下拉列表和选择器...")
        dropdown_selectors = [
            "//select",
            "//ul[contains(@class, 'dropdown')]",
            "//div[contains(@class, 'dropdown')]",
            "//div[contains(@class, 'select')]",
            "//ul[contains(@class, 'list')]"
        ]
        
        for selector in dropdown_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    print(f"    找到 {selector}: {len(elements)} 个")
            except:
                continue
                
    except Exception as e:
        print(f"  ❌ 分析页面变化时出错: {e}")


def find_department_options(driver):
    """查找发布机构/部门选项"""
    try:
        departments = ["市发改委", "市科技局", "市工信局", "发改委", "科技局", "工信局"]
        
        print("  搜索目标部门...")
        for dept in departments:
            try:
                # 尝试多种方式查找部门
                dept_selectors = [
                    f"//*[contains(text(), '{dept}')]",
                    f"//option[contains(text(), '{dept}')]",
                    f"//li[contains(text(), '{dept}')]",
                    f"//span[contains(text(), '{dept}')]",
                    f"//a[contains(text(), '{dept}')]"
                ]
                
                found = False
                for selector in dept_selectors:
                    try:
                        elements = driver.find_elements(By.XPATH, selector)
                        if elements:
                            print(f"    ✅ 找到'{dept}': {len(elements)} 个元素")
                            for i, elem in enumerate(elements[:2], 1):
                                try:
                                    tag = elem.tag_name
                                    classes = elem.get_attribute('class') or 'None'
                                    visible = elem.is_displayed()
                                    print(f"      {i}. <{tag}> class='{classes}' visible={visible}")
                                except:
                                    print(f"      {i}. 元素信息获取失败")
                            found = True
                            break
                    except:
                        continue
                
                if not found:
                    print(f"    ❌ 未找到'{dept}'")
                    
            except Exception as e:
                print(f"    ❌ 搜索'{dept}'时出错: {e}")
        
        # 尝试查找可能的AJAX加载的内容
        print("  检查可能的AJAX内容...")
        try:
            # 执行JavaScript来检查是否有动态加载的内容
            js_result = driver.execute_script("""
                // 查找所有可能包含部门信息的元素
                var elements = document.querySelectorAll('*');
                var deptElements = [];
                var keywords = ['发改委', '科技局', '工信局', '发布机构', '部门'];
                
                for (var i = 0; i < elements.length; i++) {
                    var elem = elements[i];
                    var text = elem.textContent || elem.innerText || '';
                    
                    for (var j = 0; j < keywords.length; j++) {
                        if (text.indexOf(keywords[j]) !== -1) {
                            deptElements.push({
                                tag: elem.tagName,
                                text: text.substring(0, 100),
                                className: elem.className,
                                id: elem.id,
                                visible: elem.offsetParent !== null
                            });
                            break;
                        }
                    }
                }
                
                return deptElements;
            """)
            
            if js_result:
                print(f"    JavaScript搜索找到 {len(js_result)} 个相关元素:")
                for i, elem in enumerate(js_result[:5], 1):
                    print(f"      {i}. <{elem['tag']}> text='{elem['text'][:50]}...' visible={elem['visible']}")
            else:
                print("    JavaScript搜索未找到相关元素")
                
        except Exception as e:
            print(f"    JavaScript搜索出错: {e}")
            
    except Exception as e:
        print(f"  ❌ 查找部门选项时出错: {e}")


def debug_dynamic_content():
    """调试动态内容的主函数"""
    print("="*70)
    print("           动态内容调试工具")
    print("="*70)
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("目标: 分析选择'市'级别后的动态内容变化")
    print("="*70)
    
    crawler = None
    try:
        # 初始化爬虫
        print("\n1. 初始化爬虫...")
        crawler = NanjingPolicyCrawler()
        crawler.setup_driver()
        
        # 导航到目标页面
        print("2. 导航到目标页面...")
        if not crawler.navigate_to_target():
            print("❌ 导航失败")
            return False
        
        print("✅ 成功导航到目标页面")
        
        # 等待页面完全加载
        print("3. 等待页面完全加载...")
        time.sleep(5)
        
        # 分析选择"市"级别的效果
        success = analyze_city_selection_effect(crawler.driver)
        
        if success:
            print("\n" + "="*70)
            print("✅ 动态内容调试完成!")
            print("请查看debug目录中的HTML源码和截图文件进行对比分析")
            print("="*70)
        else:
            print("\n" + "="*70)
            print("❌ 动态内容调试遇到问题!")
            print("="*70)
        
        # 保持浏览器打开一段时间供手动检查
        print("\n保持浏览器打开60秒供手动检查...")
        time.sleep(60)
        
        return success
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        return False
    finally:
        if crawler:
            crawler.cleanup()


if __name__ == "__main__":
    success = debug_dynamic_content()
    sys.exit(0 if success else 1)
