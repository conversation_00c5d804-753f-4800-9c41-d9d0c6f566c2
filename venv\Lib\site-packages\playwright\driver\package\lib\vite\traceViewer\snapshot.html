
<!DOCTYPE html>
<html lang="en">
  <body>
    <script>
      (async () => {
        if (!navigator.serviceWorker)
          throw new Error(`Service workers are not supported.\nMake sure to serve the Trace Viewer (${window.location}) via HTTPS or localhost.`);
        navigator.serviceWorker.register('sw.bundle.js');
        if (!navigator.serviceWorker.controller)
          await new Promise(f => navigator.serviceWorker.oncontrollerchange = f);
        const traceUrl = new URL(location.href).searchParams.get('trace');
        const params = new URLSearchParams();
        params.set('trace', traceUrl);
        await fetch('contexts?' + params.toString()).then(r => r.json());
        await location.reload();
      })();
    </script>
    <script src="stall.js"></script>
  </body>
</html>
