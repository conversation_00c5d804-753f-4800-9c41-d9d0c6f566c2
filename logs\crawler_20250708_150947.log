2025-07-08 15:09:47,480 - crawler.utils - INFO - NanjingPolicyCrawler initialized
2025-07-08 15:09:47,481 - crawler.utils - INFO - Using local ChromeDriver: D:\chromedriver-win64\chromedriver-win64\chromedriver.exe
2025-07-08 15:09:48,880 - crawler.utils - INFO - WebDriver setup completed successfully
2025-07-08 15:09:48,881 - crawler.utils - INFO - Navigating to: https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_market.html
2025-07-08 15:10:10,507 - crawler.utils - INFO - Successfully navigated to target website
2025-07-08 15:10:10,507 - crawler.utils - INFO - Applying complete filter process for department: 市发改委
2025-07-08 15:10:10,508 - crawler.utils - INFO - Step 1: Selecting city level in first row...
2025-07-08 15:10:10,508 - crawler.utils - INFO - Selecting city level filter in first row...
2025-07-08 15:10:12,854 - crawler.utils - INFO - Trying selector: //div[contains(@class, 'filter')]//span[contains(text(), '市')]
2025-07-08 15:10:23,415 - crawler.utils - INFO - Trying selector: //div[contains(@class, 'level')]//span[contains(text(), '市')]
2025-07-08 15:10:33,961 - crawler.utils - INFO - Trying selector: //div[contains(@class, 'row')]//span[contains(text(), '市')]
2025-07-08 15:10:44,516 - crawler.utils - INFO - Trying selector: //div[1]//span[contains(text(), '市')]
2025-07-08 15:10:44,586 - crawler.utils - INFO - Successfully selected city level in first row
2025-07-08 15:10:47,243 - crawler.utils - INFO - Waiting for second row to appear...
2025-07-08 15:10:47,243 - crawler.utils - INFO - Step 2: Expanding department filter in second row...
2025-07-08 15:10:47,243 - crawler.utils - INFO - Expanding department filter in second row...
2025-07-08 15:10:51,118 - crawler.utils - INFO - Trying expand selector: //div[contains(@class, 'second')]//span[contains(@class, 'expand')]
2025-07-08 15:11:01,648 - crawler.utils - INFO - Trying expand selector: //div[contains(@class, 'row')]//span[contains(@class, 'expand')]
2025-07-08 15:11:12,174 - crawler.utils - INFO - Trying expand selector: //div[2]//span[contains(@class, 'expand')]
2025-07-08 15:11:22,685 - crawler.utils - INFO - Trying expand selector: //tr[2]//span[contains(@class, 'expand')]
2025-07-08 15:11:33,244 - crawler.utils - INFO - Trying expand selector: //div[contains(@class, 'expand')]
2025-07-08 15:11:43,767 - crawler.utils - INFO - Trying expand selector: //span[contains(@class, 'expand')]
2025-07-08 15:11:54,320 - crawler.utils - INFO - Trying expand selector: //button[contains(@class, 'expand')]
2025-07-08 15:12:04,832 - crawler.utils - INFO - Trying expand selector: //i[contains(@class, 'expand')]
2025-07-08 15:12:15,344 - crawler.utils - INFO - Trying expand selector: //div[contains(text(), '展开')]
2025-07-08 15:12:16,466 - crawler.utils - INFO - Trying expand selector: //span[contains(text(), '展开')]
2025-07-08 15:12:27,011 - crawler.utils - INFO - Trying expand selector: //button[contains(text(), '展开')]
2025-07-08 15:12:37,550 - crawler.utils - INFO - Trying expand selector: //a[contains(text(), '展开')]
2025-07-08 15:12:38,636 - crawler.utils - INFO - Trying expand selector: //span[contains(@class, 'more')]
2025-07-08 15:12:49,156 - crawler.utils - INFO - Trying expand selector: //div[contains(@class, 'more')]
