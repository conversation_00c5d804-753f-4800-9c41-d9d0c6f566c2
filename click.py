from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import datetime
import time

def scrape_policy_market():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, slow_mo=80)
        page = browser.new_page()
        base_url = "https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/"
        page.goto(base_url + "policy_market.html", timeout=60000)

        # ✅ 点击“市”
        page.click("li[itemvalue='city']")
        time.sleep(1)

        # ✅ 展开市级单位（点击“展开”按钮）
        try:
            page.click("div.slide")  # 展开隐藏的单位
            time.sleep(1)
        except:
            print("⚠️ 未找到展开按钮，可能已展开")

        # ✅ 要抓取的市级单位列表
        dept_titles = ["市发改委", "市科技局", "市工信局"]
        all_results = []

        for dept in dept_titles:
            print(f"\n📌 正在抓取单位：{dept}")
            page.click(f"ul#city-district li[title='{dept}']")
            time.sleep(1.5)
            page.wait_for_selector("li.market-list")

            while True:
                html = page.content()
                soup = BeautifulSoup(html, "html.parser")
                for item in soup.select("li.market-list"):
                    title_tag = item.select_one("p.market-text")
                    title = title_tag["title"].strip() if title_tag else "无标题"

                    onclick = title_tag.get("onclick", "")
                    link = urljoin(base_url, onclick.split("'")[1]) if "window.open" in onclick else "无链接"

                    info = item.select_one("div.market-info")
                    all_results.append({
                        "发布单位": dept,
                        "标题": title,
                        "链接": link,
                        "简要信息": info.get_text(strip=True) if info else ""
                    })

                # 翻页逻辑
                next_btn = page.query_selector("li.pagination-next:not(.disabled)")
                if next_btn:
                    next_btn.click()
                    time.sleep(1.5)
                else:
                    break

        browser.close()
        return all_results

# ✅ 执行爬取
data = scrape_policy_market()

# ✅ 打印结果（也可以导出为 Excel）
for d in data:
    print(d)