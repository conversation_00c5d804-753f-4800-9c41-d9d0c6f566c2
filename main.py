"""
Main entry point for Nanjing Policy Crawler
"""

import sys
import os
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crawler.nanjing_policy_crawler import NanjingPolicyCrawler
from crawler.config import TARGET_DEPARTMENTS


def print_banner():
    """Print application banner"""
    print("=" * 60)
    print("           南京市政策爬虫 (Nanjing Policy Crawler)")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"目标部门: {', '.join(TARGET_DEPARTMENTS)}")
    print("=" * 60)


def print_summary(results):
    """Print crawling results summary"""
    print("\n" + "=" * 60)
    print("                    爬取结果汇总")
    print("=" * 60)
    
    total_policies = 0
    for department, data in results.items():
        count = len(data)
        total_policies += count
        print(f"{department}: {count} 条政策")
    
    print("-" * 60)
    print(f"总计: {total_policies} 条政策")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)


def main():
    """Main function"""
    try:
        print_banner()
        
        # Initialize and run crawler
        crawler = NanjingPolicyCrawler()
        results = crawler.run_crawler()
        
        # Print summary
        print_summary(results)
        
        # Check if results match expected counts from memory
        expected_counts = {
            "市发改委": 15,
            "市科技局": 26, 
            "市工信局": 7
        }
        
        print("\n预期结果对比:")
        print("-" * 40)
        for dept in TARGET_DEPARTMENTS:
            actual = len(results.get(dept, []))
            expected = expected_counts.get(dept, 0)
            status = "✓" if actual == expected else "✗"
            print(f"{dept}: 实际 {actual} / 预期 {expected} {status}")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n用户中断了爬取过程")
        return 1
    except Exception as e:
        print(f"\n\n爬取过程中发生错误: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
