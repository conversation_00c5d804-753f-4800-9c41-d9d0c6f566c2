"""
Test script for the updated filtering logic
"""

import sys
import os
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crawler.nanjing_policy_crawler import NanjingPolicyCrawler
from crawler.config import TARGET_DEPARTMENTS


def test_filtering_for_single_department():
    """Test filtering logic for a single department"""
    print("=" * 70)
    print("           筛选逻辑测试 (Filtering Logic Test)")
    print("=" * 70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("测试目标: 验证两行筛选逻辑是否正确工作")
    print("=" * 70)
    
    crawler = None
    try:
        # Initialize crawler
        print("\n1. 初始化爬虫...")
        crawler = NanjingPolicyCrawler()
        
        # Setup WebDriver
        print("2. 设置WebDriver...")
        crawler.setup_driver()
        
        # Test with first department only
        test_department = TARGET_DEPARTMENTS[0]  # 市发改委
        print(f"\n3. 测试部门筛选: {test_department}")
        
        # Navigate to target page
        print("4. 导航到目标页面...")
        if not crawler.navigate_to_target():
            print("❌ 导航失败")
            return False
        
        print("✅ 成功导航到目标页面")
        
        # Test the complete filtering process
        print(f"5. 应用完整筛选流程...")
        if crawler.apply_filters_for_department(test_department):
            print(f"✅ 成功应用筛选条件: {test_department}")
            
            # Try to get policy links
            print("6. 提取政策链接...")
            policy_links = crawler.get_policy_links()
            
            if policy_links:
                print(f"✅ 成功找到 {len(policy_links)} 个政策链接")
                print("前5个链接:")
                for i, link in enumerate(policy_links[:5], 1):
                    print(f"  {i}. {link}")
                return True
            else:
                print("⚠️ 未找到政策链接，可能需要调整选择器")
                return False
        else:
            print(f"❌ 筛选失败: {test_department}")
            return False
            
    except KeyboardInterrupt:
        print("\n\n用户中断了测试过程")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        return False
    finally:
        if crawler:
            crawler.cleanup()
            print("\n7. 清理资源完成")


def main():
    """Main test function"""
    success = test_filtering_for_single_department()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ 筛选逻辑测试通过!")
        print("可以继续运行完整爬虫: python main.py")
    else:
        print("❌ 筛选逻辑测试失败!")
        print("需要进一步调整选择器策略")
    print("=" * 70)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
