# 南京市政策爬虫 (Nanjing Policy Crawler)

基于Selenium的南京市政府政策网站爬虫，用于自动抓取指定部门的政策文件信息。

## 项目概述

### 目标网站
- **URL**: https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_market.html
- **类型**: 南京市政府政策发布平台

### 爬取范围
- **发布层级**: 市
- **目标部门**: 市发改委、市科技局、市工信局
- **数据字段**: title、create_time、address、content、fbjg、link

## 技术架构

- **主要工具**: Selenium WebDriver
- **浏览器**: Chrome + ChromeDriver (自动管理)
- **编程语言**: Python 3.7+
- **数据存储**: JSON/CSV格式

## 项目结构

```
vx_crawler/
├── crawler/
│   ├── __init__.py
│   ├── nanjing_policy_crawler.py  # 主爬虫类
│   ├── config.py                  # 配置文件
│   └── utils.py                   # 工具函数
├── data/                          # 数据存储目录
├── logs/                          # 日志文件
├── requirements.txt               # 依赖包
├── main.py                        # 主程序入口
├── test_basic.py                  # 基础测试
├── test_crawler_structure.py      # 结构测试
└── README.md                      # 说明文档
```

## 安装说明

### 1. 环境要求
- Python 3.7 或更高版本
- Chrome浏览器 (最新版本)
- 网络连接

### 2. 安装依赖
```bash
# 激活虚拟环境 (如果使用)
venv\Scripts\activate  # Windows
# 或
source venv/bin/activate  # Linux/Mac

# 安装依赖包
pip install -r requirements.txt
```

### 3. 验证安装
```bash
# 运行基础测试
python test_basic.py

# 运行结构测试
python test_crawler_structure.py
```

## 使用方法

### 基本使用
```bash
python main.py
```

### 程序流程
1. 初始化Selenium WebDriver
2. 访问目标页面
3. 选择"市"级别筛选
4. 展开部门筛选选项
5. 逐个选择目标部门（市发改委、市科技局、市工信局）
6. 提取政策列表链接
7. 访问每个政策详情页面
8. 提取所需数据字段
9. 保存数据到JSON和CSV文件
10. 生成爬取报告

### 输出文件
- **JSON格式**: `data/nanjing_policies_[部门]_[时间戳].json`
- **CSV格式**: `data/nanjing_policies_[部门]_[时间戳].csv`
- **合并文件**: `data/nanjing_policies_all_[时间戳].json`
- **日志文件**: `logs/crawler_[时间戳].log`

## 数据结构

### 输出数据格式
```json
{
  "title": "政策标题",
  "create_time": "政策发布时间",
  "address": "详情页URL",
  "content": "政策正文内容（纯文字）",
  "fbjg": "发布机构",
  "link": ["附件链接1", "附件链接2"]
}
```

## 配置说明

### 主要配置项 (crawler/config.py)
- `TARGET_URL`: 目标网站URL
- `TARGET_DEPARTMENTS`: 目标部门列表
- `SELENIUM_CONFIG`: Selenium配置参数
- `OUTPUT_CONFIG`: 输出文件配置

### 反爬虫设置
- 随机请求间隔 (2-5秒)
- 随机User-Agent轮换
- 页面加载超时控制
- 元素等待机制

## 预期结果

根据人工筛选验证，预期爬取结果：
- **市发改委**: 15条政策
- **市科技局**: 26条政策  
- **市工信局**: 7条政策

## 注意事项

1. **合规性**: 遵守robots.txt协议和网站使用条款
2. **频率控制**: 避免对目标网站造成过大压力
3. **数据质量**: 确保提取数据的准确性和完整性
4. **网络稳定**: 确保网络连接稳定，避免中途断网
5. **浏览器版本**: 保持Chrome浏览器为最新版本

## 故障排除

### 常见问题
1. **ChromeDriver版本不匹配**: 程序会自动下载匹配的ChromeDriver
2. **网络超时**: 检查网络连接，可能需要重新运行
3. **页面结构变化**: 网站更新可能需要调整选择器
4. **权限问题**: 确保有写入data和logs目录的权限

### 调试模式
修改 `crawler/config.py` 中的 `SELENIUM_CONFIG["headless"] = False` 可以看到浏览器操作过程。

## 开发说明

### 扩展功能
- 支持更多政府部门
- 增加数据去重功能
- 支持断点续爬
- 增加监控和报警机制

### 代码结构
- `NanjingPolicyCrawler`: 主爬虫类
- `utils.py`: 通用工具函数
- `config.py`: 配置管理
- 模块化设计，便于维护和扩展
