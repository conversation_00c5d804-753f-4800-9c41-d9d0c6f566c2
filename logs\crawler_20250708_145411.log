2025-07-08 14:54:11,434 - crawler.utils - INFO - NanjingPolicyCrawler initialized
2025-07-08 14:54:11,435 - crawler.utils - INFO - Starting Nanjing Policy Crawler...
2025-07-08 14:54:11,435 - WDM - INFO - ====== WebDriver manager ======
2025-07-08 14:54:12,080 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-08 14:54:13,863 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-08 14:54:15,297 - WDM - INFO - There is no [win64] chromedriver "138.0.7204.92" for browser google-chrome "138.0.7204" in cache
2025-07-08 14:54:15,297 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-08 14:54:18,698 - WDM - INFO - WebDriver version 138.0.7204.92 selected
2025-07-08 14:54:18,700 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.92/win32/chromedriver-win32.zip
2025-07-08 14:54:18,700 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.92/win32/chromedriver-win32.zip
2025-07-08 14:54:19,584 - WDM - INFO - Driver downloading response is 200
2025-07-08 14:54:36,383 - crawler.utils - ERROR - Failed to setup WebDriver: ('Connection broken: IncompleteRead(1196032 bytes read, 7836785 more expected)', IncompleteRead(1196032 bytes read, 7836785 more expected))
2025-07-08 14:54:36,383 - crawler.utils - ERROR - Error running crawler: ('Connection broken: IncompleteRead(1196032 bytes read, 7836785 more expected)', IncompleteRead(1196032 bytes read, 7836785 more expected))
