"""
测试改进后的爬虫 - 基于click.py的成功经验
"""

import sys
import os
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crawler.nanjing_policy_crawler import NanjingPolicyCrawler
from crawler.config import TARGET_DEPARTMENTS


def test_improved_filtering():
    """测试改进后的筛选逻辑"""
    print("="*70)
    print("           改进后的爬虫测试")
    print("="*70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("基于click.py的成功经验进行改进")
    print("="*70)
    
    crawler = None
    try:
        # 初始化爬虫
        print("\n1. 初始化爬虫...")
        crawler = NanjingPolicyCrawler()
        crawler.setup_driver()
        
        # 导航到目标页面
        print("2. 导航到目标页面...")
        if not crawler.navigate_to_target():
            print("❌ 导航失败")
            return False
        
        print("✅ 成功导航到目标页面")
        
        # 测试第一个部门的完整流程
        test_department = TARGET_DEPARTMENTS[0]  # 市发改委
        print(f"\n3. 测试部门: {test_department}")
        
        # 步骤1: 选择"市"级别
        print("   步骤1: 选择'市'级别...")
        if not crawler.select_city_level():
            print("   ❌ 选择'市'级别失败")
            return False
        print("   ✅ 成功选择'市'级别")
        
        # 步骤2: 展开部门筛选
        print("   步骤2: 展开部门筛选...")
        if not crawler.expand_department_filter():
            print("   ❌ 展开部门筛选失败")
            return False
        print("   ✅ 成功展开部门筛选")
        
        # 步骤3: 选择具体部门
        print(f"   步骤3: 选择部门 {test_department}...")
        if not crawler.select_department(test_department):
            print(f"   ❌ 选择部门 {test_department} 失败")
            return False
        print(f"   ✅ 成功选择部门 {test_department}")
        
        # 步骤4: 提取政策链接
        print("   步骤4: 提取政策链接...")
        policy_links = crawler.get_policy_links()
        
        if policy_links:
            print(f"   ✅ 成功提取 {len(policy_links)} 个政策链接")
            print("   前5个链接:")
            for i, link in enumerate(policy_links[:5], 1):
                print(f"     {i}. {link}")
            
            # 步骤5: 测试提取一个政策的详细信息
            if len(policy_links) > 0:
                print("   步骤5: 测试提取政策详细信息...")
                test_link = policy_links[0]
                policy_data = crawler.extract_policy_data(test_link)
                
                if policy_data:
                    print("   ✅ 成功提取政策详细信息:")
                    print(f"     标题: {policy_data.get('title', 'N/A')[:50]}...")
                    print(f"     发布时间: {policy_data.get('create_time', 'N/A')}")
                    print(f"     发布机构: {policy_data.get('fbjg', 'N/A')}")
                    print(f"     内容长度: {len(policy_data.get('content', ''))}")
                    print(f"     附件数量: {len(policy_data.get('link', []))}")
                else:
                    print("   ⚠️ 政策详细信息提取失败")
            
            return True
        else:
            print("   ❌ 未找到政策链接")
            return False
            
    except KeyboardInterrupt:
        print("\n\n用户中断了测试过程")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        return False
    finally:
        if crawler:
            print("\n6. 保持浏览器打开30秒供检查...")
            import time
            time.sleep(30)
            crawler.cleanup()


def test_all_departments():
    """测试所有部门的筛选"""
    print("="*70)
    print("           测试所有部门筛选")
    print("="*70)
    
    crawler = None
    results = {}
    
    try:
        crawler = NanjingPolicyCrawler()
        crawler.setup_driver()
        
        for i, department in enumerate(TARGET_DEPARTMENTS, 1):
            print(f"\n{i}. 测试部门: {department}")
            
            # 导航到目标页面
            if not crawler.navigate_to_target():
                print(f"   ❌ 导航失败")
                results[department] = 0
                continue
            
            # 应用筛选条件
            if not crawler.apply_filters_for_department(department):
                print(f"   ❌ 筛选失败")
                results[department] = 0
                continue
            
            # 提取政策链接
            policy_links = crawler.get_policy_links()
            count = len(policy_links)
            results[department] = count
            
            print(f"   ✅ 找到 {count} 个政策")
            
            # 添加延迟避免请求过快
            import time
            time.sleep(3)
        
        # 显示结果汇总
        print("\n" + "="*70)
        print("           测试结果汇总")
        print("="*70)
        
        expected_counts = {
            "市发改委": 15,
            "市科技局": 26,
            "市工信局": 7
        }
        
        total_actual = 0
        total_expected = 0
        
        for dept in TARGET_DEPARTMENTS:
            actual = results.get(dept, 0)
            expected = expected_counts.get(dept, 0)
            total_actual += actual
            total_expected += expected
            
            status = "✅" if actual > 0 else "❌"
            print(f"{dept}: 实际 {actual} / 预期 {expected} {status}")
        
        print("-" * 50)
        print(f"总计: 实际 {total_actual} / 预期 {total_expected}")
        
        success_rate = len([r for r in results.values() if r > 0]) / len(TARGET_DEPARTMENTS)
        print(f"成功率: {success_rate:.1%}")
        
        return success_rate > 0.5  # 至少50%的部门成功
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
    finally:
        if crawler:
            crawler.cleanup()


def main():
    """主测试函数"""
    print("选择测试模式:")
    print("1. 单部门详细测试")
    print("2. 所有部门快速测试")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            success = test_improved_filtering()
        elif choice == "2":
            success = test_all_departments()
        else:
            print("无效选择，默认执行单部门测试")
            success = test_improved_filtering()
        
        print("\n" + "="*70)
        if success:
            print("✅ 测试成功! 改进后的爬虫工作正常")
            print("可以运行完整爬虫: python main.py")
        else:
            print("❌ 测试失败! 需要进一步调试")
        print("="*70)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
