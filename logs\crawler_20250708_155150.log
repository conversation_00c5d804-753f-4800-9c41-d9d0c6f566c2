2025-07-08 15:51:50,888 - crawler.utils - INFO - NanjingPolicyCrawler initialized
2025-07-08 15:51:50,889 - crawler.utils - INFO - Using local ChromeDriver: D:\chromedriver-win64\chromedriver-win64\chromedriver.exe
2025-07-08 15:51:52,466 - crawler.utils - INFO - WebDriver setup completed successfully
2025-07-08 15:51:52,466 - crawler.utils - INFO - Navigating to: https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_market.html
2025-07-08 15:52:14,230 - crawler.utils - INFO - Successfully navigated to target website
2025-07-08 15:52:14,231 - crawler.utils - INFO - Selecting city level using proven selector...
2025-07-08 15:52:16,245 - crawler.utils - INFO - ✅ Successfully selected city level using li[itemvalue='city']
2025-07-08 15:52:17,247 - crawler.utils - INFO - Expanding department filter using proven selector...
2025-07-08 15:52:19,334 - crawler.utils - WARNING - CSS selector div.slide failed: Message: element click intercepted: Element <div class="slide" style="display: block;">...</div> is not clickable at point (997, 308). Other element would receive the click: <a class="animation-link" href="#" onclick="gojkf()" title="点击跳转智能客服"></a>
  (Session info: chrome=138.0.7204.51)
Stacktrace:
	GetHandleVerifier [0x0x7ff69ddd6f95+76917]
	GetHandleVerifier [0x0x7ff69ddd6ff0+77008]
	(No symbol) [0x0x7ff69db89dea]
	(No symbol) [0x0x7ff69dbe8269]
	(No symbol) [0x0x7ff69dbe5c02]
	(No symbol) [0x0x7ff69dbe2c41]
	(No symbol) [0x0x7ff69dbe1b31]
	(No symbol) [0x0x7ff69dbd3368]
	(No symbol) [0x0x7ff69dc0846a]
	(No symbol) [0x0x7ff69dbd2c16]
	(No symbol) [0x0x7ff69dc08680]
	(No symbol) [0x0x7ff69dc3065c]
	(No symbol) [0x0x7ff69dc08243]
	(No symbol) [0x0x7ff69dbd1431]
	(No symbol) [0x0x7ff69dbd21c3]
	GetHandleVerifier [0x0x7ff69e0ad2cd+3051437]
	GetHandleVerifier [0x0x7ff69e0a7923+3028483]
	GetHandleVerifier [0x0x7ff69e0c58bd+3151261]
	GetHandleVerifier [0x0x7ff69ddf185e+185662]
	GetHandleVerifier [0x0x7ff69ddf971f+218111]
	GetHandleVerifier [0x0x7ff69dddfb14+112628]
	GetHandleVerifier [0x0x7ff69dddfcc9+113065]
	GetHandleVerifier [0x0x7ff69ddc6c98+10616]
	BaseThreadInitThunk [0x0x7fffbdfce8d7+23]
	RtlUserThreadStart [0x0x7fffbe65c34c+44]
, trying XPath alternatives...
2025-07-08 15:52:19,334 - crawler.utils - INFO - Trying XPath expand selector: //div[@class='slide']
2025-07-08 15:52:20,395 - crawler.utils - INFO - Trying XPath expand selector: //div[contains(@class, 'slide')]
2025-07-08 15:52:21,475 - crawler.utils - INFO - Trying XPath expand selector: //div[contains(text(), '展开')]
2025-07-08 15:52:22,604 - crawler.utils - INFO - Trying XPath expand selector: //span[contains(text(), '展开')]
2025-07-08 15:52:33,118 - crawler.utils - WARNING - ⚠️ No expand button found, department filter might already be expanded
2025-07-08 15:52:33,118 - crawler.utils - INFO - Selecting department: 市发改委
2025-07-08 15:52:44,031 - crawler.utils - WARNING - CSS selector failed: Message: 
, trying XPath alternatives...
2025-07-08 15:52:44,031 - crawler.utils - INFO - Trying XPath department selector: //ul[@id='city-district']//li[@title='市发改委']
2025-07-08 15:52:54,442 - crawler.utils - INFO - Trying XPath department selector: //ul[@id='city-district']//li[contains(@title, '市发改委')]
2025-07-08 15:53:04,788 - crawler.utils - INFO - Trying XPath department selector: //li[@title='市发改委']
2025-07-08 15:53:15,166 - crawler.utils - INFO - Trying XPath department selector: //li[contains(@title, '市发改委')]
2025-07-08 15:53:25,587 - crawler.utils - ERROR - ❌ All department selectors failed for: 市发改委
2025-07-08 15:53:57,694 - crawler.utils - INFO - WebDriver closed successfully
