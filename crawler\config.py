"""
Configuration file for Nanjing Policy Crawler
"""

# Target website configuration
TARGET_URL = "https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_market.html"

# Target departments to crawl
TARGET_DEPARTMENTS = [
    "市发改委",
    "市科技局", 
    "市工信局"
]

# Data fields to extract
DATA_FIELDS = [
    "title",
    "create_time", 
    "address",
    "content",
    "fbjg",
    "link"
]

# Selenium configuration
SELENIUM_CONFIG = {
    "implicit_wait": 10,
    "page_load_timeout": 30,
    "request_delay": (2, 5),  # Random delay between requests (min, max) seconds
    "headless": False,  # Set to True for headless mode
}

# Output configuration
OUTPUT_CONFIG = {
    "data_dir": "data",
    "logs_dir": "logs",
    "json_output": True,
    "csv_output": True,
    "include_timestamp": True
}

# User agents for rotation
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
]
