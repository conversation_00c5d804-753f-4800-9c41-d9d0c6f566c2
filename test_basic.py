"""
Basic test to verify the crawler structure and imports
"""

import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all modules can be imported"""
    try:
        print("Testing imports...")
        
        # Test config import
        from crawler.config import TARGET_URL, TARGET_DEPARTMENTS
        print(f"✓ Config imported successfully")
        print(f"  Target URL: {TARGET_URL}")
        print(f"  Target Departments: {TARGET_DEPARTMENTS}")
        
        # Test utils import
        from crawler.utils import setup_logging, clean_text
        print(f"✓ Utils imported successfully")
        
        # Test basic functionality
        test_text = "  This is a   test   text  "
        cleaned = clean_text(test_text)
        print(f"✓ Text cleaning works: '{test_text}' -> '{cleaned}'")
        
        print("\n✓ All basic imports and functions work correctly!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def test_directory_structure():
    """Test if all required directories exist"""
    try:
        print("\nTesting directory structure...")
        
        required_dirs = ["crawler", "data", "logs"]
        required_files = [
            "crawler/__init__.py",
            "crawler/config.py", 
            "crawler/utils.py",
            "crawler/nanjing_policy_crawler.py",
            "main.py",
            "requirements.txt"
        ]
        
        # Check directories
        for directory in required_dirs:
            if os.path.exists(directory):
                print(f"✓ Directory exists: {directory}")
            else:
                print(f"✗ Directory missing: {directory}")
                return False
        
        # Check files
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✓ File exists: {file_path}")
            else:
                print(f"✗ File missing: {file_path}")
                return False
        
        print("\n✓ All required directories and files exist!")
        return True
        
    except Exception as e:
        print(f"✗ Error checking directory structure: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("           基础结构测试 (Basic Structure Test)")
    print("=" * 60)
    
    success = True
    
    # Test directory structure
    if not test_directory_structure():
        success = False
    
    # Test imports
    if not test_imports():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 所有基础测试通过! (All basic tests passed!)")
        print("可以继续安装依赖包并运行完整爬虫。")
    else:
        print("✗ 基础测试失败! (Basic tests failed!)")
        print("请检查项目结构和代码。")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
