"""
Installation script for Nanjing Policy Crawler dependencies
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and return success status"""
    try:
        print(f"\n{description}...")
        print(f"执行命令: {command}")
        
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        
        print("✓ 成功!")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 失败: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"✗ 异常: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("✓ Python版本兼容")
        return True
    else:
        print("✗ Python版本过低，需要3.7或更高版本")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("=" * 60)
    print("           依赖包安装脚本")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check if requirements.txt exists
    if not os.path.exists("requirements.txt"):
        print("✗ requirements.txt 文件不存在")
        return False
    
    print("\n正在安装依赖包...")
    
    # Install packages one by one for better error handling
    packages = [
        "selenium==4.15.2",
        "webdriver-manager==4.0.1", 
        "beautifulsoup4==4.12.2",
        "lxml==4.9.3",
        "requests==2.31.0",
        "pandas==2.1.3"
    ]
    
    success_count = 0
    
    for package in packages:
        if run_command(f"pip install {package}", f"安装 {package}"):
            success_count += 1
        else:
            print(f"警告: {package} 安装失败，但继续安装其他包...")
    
    print(f"\n安装结果: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("✓ 所有依赖包安装成功!")
        return True
    elif success_count >= 4:  # At least selenium and webdriver-manager
        print("⚠ 部分依赖包安装成功，可以尝试运行爬虫")
        return True
    else:
        print("✗ 关键依赖包安装失败")
        return False

def verify_installation():
    """Verify that key packages can be imported"""
    print("\n验证安装...")
    
    try:
        import selenium
        print(f"✓ Selenium {selenium.__version__} 安装成功")
    except ImportError:
        print("✗ Selenium 导入失败")
        return False
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        print("✓ WebDriver Manager 安装成功")
    except ImportError:
        print("✗ WebDriver Manager 导入失败")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✓ BeautifulSoup 安装成功")
    except ImportError:
        print("✗ BeautifulSoup 导入失败")
        return False
    
    print("✓ 关键依赖包验证通过!")
    return True

def main():
    """Main installation function"""
    try:
        # Install dependencies
        if not install_dependencies():
            print("\n安装失败，请手动安装依赖包:")
            print("pip install -r requirements.txt")
            return 1
        
        # Verify installation
        if not verify_installation():
            print("\n验证失败，请检查安装")
            return 1
        
        print("\n" + "=" * 60)
        print("✓ 安装完成!")
        print("\n下一步:")
        print("1. 运行基础测试: python test_basic.py")
        print("2. 运行结构测试: python test_crawler_structure.py") 
        print("3. 运行爬虫: python main.py")
        print("=" * 60)
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n用户中断了安装过程")
        return 1
    except Exception as e:
        print(f"\n\n安装过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
